import { Component } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { BehaviorSubject, filter } from 'rxjs';
import { DrawerComponent } from '../drawer/drawer.component';

@UntilDestroy()
@Component({
  selector: 'app-layout',
  templateUrl: './layout.component.html',
  styleUrl: './layout.component.css'
})
export class LayoutComponent {
  public currentPage: string = 'dashboard';
  public isDrawerOpen: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);

  constructor(private router: Router) {
    this.router.events.pipe(
      filter((e) => e instanceof NavigationEnd)
    ).subscribe((e) => {
      const event = e as NavigationEnd;
      if (event) {
        this.currentPage = event.urlAfterRedirects.replaceAll('/', '');
      }
    })
  }

  public openDrawer(): void {
    this.isDrawerOpen.next(true);
  }

  public closeDrawer(): void {
    this.isDrawerOpen.next(false);
  }
}
