<!-- Modern Chat Message Component -->
<div class="chat-message"
    [class.user-message]="isUserMessage"
    [class.support-message]="isSupportResponse"
    [class.system-message]="isSystemNotification">

    @if (isSystemNotification) {
        <!-- System/Status Change Message -->
        <div class="system-notification">
            <div class="system-content">
                <div class="system-icon" [innerHTML]="messageIcon"></div>
                <div class="system-text">
                    <span class="system-message-text">{{ message.content }}</span>
                    <time class="system-time font-mono">{{ message.createdAt | date:"yyyy/MM/dd, HH:mm" }}</time>
                </div>
            </div>
        </div>
    } @else {
        <!-- User/Support Chat Message -->
        <div class="message-wrapper">
            <!-- Avatar -->
            <div class="message-avatar">
                <div class="avatar-icon" [innerHTML]="messageIcon"></div>
            </div>

            <!-- Message Content -->
            <div class="message-content-wrapper">
                <!-- Message Header -->
                <div class="message-header">
                    <span class="author-name">{{ authorDisplayName }}</span>
                    <time class="message-timestamp font-mono">{{ message.createdAt | date:"yyyy/MM/dd, HH:mm" }}</time>
                </div>

                <!-- Message Bubble -->
                <div class="message-bubble">
                    <div class="message-text">{{ message.content }}</div>

                    <!-- Attachments -->
                    @if (message.attachments && message.attachments.length > 0) {
                        <div class="attachments-container">
                            <div class="attachments-header">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="m18.375 12.739-7.693 7.693a4.5 4.5 0 0 1-6.364-6.364l10.94-10.94A3 3 0 1 1 19.5 7.372L8.552 18.32m.009-.01-.01.01m5.699-9.941-7.81 7.81a1.5 1.5 0 0 0 2.112 2.13" />
                                </svg>
                                <span class="attachments-count">{{ message.attachments.length }} فایل پیوست</span>
                            </div>
                            <div class="attachments-grid">
                                @for (attachment of message.attachments; track attachment.id) {
                                    <div class="attachment-card">
                                        <div class="attachment-icon">
                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z" />
                                            </svg>
                                        </div>
                                        <div class="attachment-info">
                                            <div class="attachment-name">{{ attachment.fileName }}</div>
                                            <div class="attachment-size">{{ formatFileSize(attachment.fileSize) }}</div>
                                        </div>
                                        <a [href]="attachment.downloadUrl" class="attachment-download" download>
                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3" />
                                            </svg>
                                        </a>
                                    </div>
                                }
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    }
</div>
